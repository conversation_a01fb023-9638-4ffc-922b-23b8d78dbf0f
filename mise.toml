[tools]
tflint = "latest"
pre-commit = "latest"
terraform = "1.10"
uv = "latest"
terraform-docs = "latest"

[tasks]
lint = ["terraform fmt -recursive", "tflint --recursive"]
setup = ["pre-commit install", "tflint --init"]
validate = [
    "terraform init && terraform validate",
    "cd examples/braintrust-data-plane && terraform init && terraform validate",
]
precommit = ["pre-commit run --all-files"]
