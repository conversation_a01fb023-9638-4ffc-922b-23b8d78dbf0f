#!/usr/bin/env -S uv --quiet run --script
# /// script
# dependencies = [
#   "boto3",
# ]
# ///

"""
This script locks specific versions of lambdas and containers as a part of the
release process of the terraform module.

Versions are written to a VERSIONS.json file in their respective module directories.
The terraform module will look at these files to determine the versions to deploy.

By default, this script will write the versions from the `latest` tag.
You can specify a different tag (e.g. stable) by passing it as an argument.

Usage:
    ./lock_versions [version_tag]

    If no version_tag is provided, "latest" is used by default.
"""

import json
import urllib.request
import sys
import os
import boto3
import botocore.exceptions
import datetime


def check_aws_authentication():
    try:
        session = boto3.Session()
        sts_client = session.client("sts")
        sts_client.get_caller_identity()
        return True
    except (
        botocore.exceptions.ClientError,
        botocore.exceptions.TokenRetrievalError,
    ) as e:
        return False
    except Exception as e:
        print(f"Unexpected error checking AWS authentication: {str(e)}")
        raise


def get_container_version(repository_name, version_tag):
    """
    Finds the commit hash for a container image based on a tag like "latest".
    """
    print(f"Fetching {repository_name} version from ECR for tag: {version_tag}...")

    session = boto3.Session()
    ecr_client = session.client("ecr-public", region_name="us-east-1")

    response = ecr_client.describe_images(
        repositoryName=repository_name, imageIds=[{"imageTag": version_tag}]
    )

    if not response["imageDetails"]:
        raise Exception(f"No image found for {repository_name}:{version_tag}")

    image_tags = response["imageDetails"][0].get("imageTags", [])

    # Only include tags that are long enough to be commit hashes
    # This is hacky, but is better than enumerating all the valid tags
    version_tags = [tag for tag in image_tags if len(tag) >= 40]
    if len(version_tags) == 0:
        raise Exception(
            f"No commit hash tags found for {repository_name}:{version_tag}. Can't find the specific version to lock onto."
        )
    elif len(version_tags) > 1:
        raise Exception(
            f"Multiple commit hash tags found for {repository_name}:{version_tag} : {version_tags}. Expected only one hash tag. Unsure which version to lock onto."
        )

    version = version_tags[0]

    print(f"Found {repository_name} version: {version}")
    return {repository_name: version}


def get_lambda_versions(version_tag):
    """
    Fetches versions for all lambda functions based on the specified tag.
    Returns a dictionary with lambda names as keys and versions as values.
    """
    print(f"Fetching lambda versions for tag: {version_tag}")

    lambdas = [
        "AIProxy",
        "APIHandler",
        "MigrateDatabaseFunction",
        "QuarantineWarmupFunction",
        "CatchupETL",
    ]

    lambda_base_url = (
        "https://braintrust-assets-us-east-1.s3.us-east-1.amazonaws.com/lambda"
    )
    versions = {}
    for lambda_name in lambdas:
        print(f"Fetching {version_tag} version for {lambda_name}...")
        with urllib.request.urlopen(
            f"{lambda_base_url}/{lambda_name}/version-{version_tag}"
        ) as response:
            version = response.read().decode("utf-8").strip()
            versions[lambda_name] = version

    return versions


def main():
    version_tag = "latest"
    if len(sys.argv) > 1:
        version_tag = sys.argv[1]

    script_dir = os.path.dirname(os.path.abspath(__file__))
    metadata = {
        "_tag": version_tag,
        "_timestamp": datetime.datetime.now().isoformat(),
    }

    if not check_aws_authentication():
        print("ERROR: You are not authenticated with AWS.")
        print("Please run 'aws sso login' or set up your AWS credentials.")
        sys.exit(1)

    # Get lambda versions and write to services/VERSIONS.json
    lambda_versions = get_lambda_versions(version_tag)
    lambda_versions.update(metadata)

    print("Writing lambda versions to modules/services/VERSIONS.json...")
    versions_path = f"{script_dir}/modules/services/VERSIONS.json"
    with open(versions_path, "w") as f:
        json.dump(lambda_versions, f, indent=4)

    # Get brainstore version from ECR and write to brainstore/VERSIONS.json
    brainstore_version = get_container_version("brainstore", version_tag)
    brainstore_version.update(metadata)
    print("Writing brainstore version to modules/brainstore/VERSIONS.json...")
    brainstore_versions_path = f"{script_dir}/modules/brainstore/VERSIONS.json"
    os.makedirs(os.path.dirname(brainstore_versions_path), exist_ok=True)
    with open(brainstore_versions_path, "w") as f:
        json.dump(brainstore_version, f, indent=4)


if __name__ == "__main__":
    main()
